<template>
  <div class="search-layout-example">
    <div class="page-header">
      <h1>工单管理 - 搜索布局示例</h1>
      <p>这是一个使用 CollapsibleSearchLayout 组件的完整示例</p>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <CollapsibleSearchLayout
        :conditions="searchConditions"
        :default-expanded="false"
        :items-per-row="3"
        @search="handleSearch"
        @toggle="handleToggle"
      >
        <!-- 工单编号 -->
        <template #condition-0>
          <a-form-item label="工单编号" class="search-form-item">
            <a-input
              v-model:value="searchForm.workOrderNo"
              placeholder="请输入工单编号"
              allow-clear
            />
          </a-form-item>
        </template>

        <!-- 产品编号 -->
        <template #condition-1>
          <a-form-item label="产品编号" class="search-form-item">
            <a-input
              v-model:value="searchForm.productNo"
              placeholder="请输入产品编号"
              allow-clear
            />
          </a-form-item>
        </template>

        <!-- 产品名称 -->
        <template #condition-2>
          <a-form-item label="产品名称" class="search-form-item">
            <a-input
              v-model:value="searchForm.productName"
              placeholder="请输入产品名称"
              allow-clear
            />
          </a-form-item>
        </template>

        <!-- 工单状态 -->
        <template #condition-3>
          <a-form-item label="工单状态" class="search-form-item">
            <a-select v-model:value="searchForm.status" placeholder="请选择工单状态" allow-clear>
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="draft">草稿</a-select-option>
              <a-select-option value="pending">待处理</a-select-option>
              <a-select-option value="processing">处理中</a-select-option>
              <a-select-option value="completed">已完成</a-select-option>
              <a-select-option value="cancelled">已取消</a-select-option>
            </a-select>
          </a-form-item>
        </template>

        <!-- 紧急程度 -->
        <template #condition-4>
          <a-form-item label="紧急程度" class="search-form-item">
            <a-select v-model:value="searchForm.urgency" placeholder="请选择紧急程度" allow-clear>
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="low">低</a-select-option>
              <a-select-option value="medium">中</a-select-option>
              <a-select-option value="high">高</a-select-option>
              <a-select-option value="urgent">紧急</a-select-option>
            </a-select>
          </a-form-item>
        </template>

        <!-- 创建时间范围 -->
        <template #condition-5>
          <a-form-item label="创建时间" class="search-form-item">
            <a-range-picker
              v-model:value="searchForm.createTimeRange"
              style="width: 100%"
              placeholder="['开始时间', '结束时间']"
            />
          </a-form-item>
        </template>

        <!-- 负责人 -->
        <template #condition-6>
          <a-form-item label="负责人" class="search-form-item">
            <a-select
              v-model:value="searchForm.assignee"
              placeholder="请选择负责人"
              allow-clear
              show-search
              :filter-option="filterOption"
            >
              <a-select-option v-for="user in userList" :key="user.id" :value="user.id">
                {{ user.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </template>

        <!-- 压铸机 -->
        <template #condition-7>
          <a-form-item label="压铸机" class="search-form-item">
            <a-select v-model:value="searchForm.machine" placeholder="请选择压铸机" allow-clear>
              <a-select-option value="">全部</a-select-option>
              <a-select-option value="machine-01">压铸机-01</a-select-option>
              <a-select-option value="machine-02">压铸机-02</a-select-option>
              <a-select-option value="machine-03">压铸机-03</a-select-option>
            </a-select>
          </a-form-item>
        </template>
      </CollapsibleSearchLayout>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <a-space>
        <a-button type="primary" @click="handleAdd">新建工单</a-button>
        <a-button @click="handleExport">导出数据</a-button>
        <a-button @click="handleReset">重置条件</a-button>
      </a-space>
    </div>

    <!-- 搜索结果展示 -->
    <div class="result-section">
      <a-card title="搜索结果" :bordered="false">
        <template #extra>
          <a-space>
            <span>共 {{ mockData.length }} 条记录</span>
            <a-button size="small" @click="handleRefresh">刷新</a-button>
          </a-space>
        </template>

        <a-table
          :columns="tableColumns"
          :data-source="mockData"
          :pagination="{ pageSize: 10 }"
          size="middle"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'urgency'">
              <a-tag :color="getUrgencyColor(record.urgency)">
                {{ getUrgencyText(record.urgency) }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" size="small">查看</a-button>
                <a-button type="link" size="small">编辑</a-button>
                <a-button type="link" size="small" danger>删除</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import CollapsibleSearchLayout from '@/components/CollapsibleSearchLayout/index.vue'

// 搜索表单数据
const searchForm = ref({
  workOrderNo: '',
  productNo: '',
  productName: '',
  status: '',
  urgency: '',
  createTimeRange: null,
  assignee: '',
  machine: '',
})

// 搜索条件配置
const searchConditions = ref([
  { label: '工单编号' },
  { label: '产品编号' },
  { label: '产品名称' },
  { label: '工单状态' },
  { label: '紧急程度' },
  { label: '创建时间' },
  { label: '负责人' },
  { label: '压铸机' },
])

// 用户列表
const userList = ref([
  { id: '1', name: '张三' },
  { id: '2', name: '李四' },
  { id: '3', name: '王五' },
  { id: '4', name: '赵六' },
])

// 表格列配置
const tableColumns = [
  { title: '工单编号', dataIndex: 'workOrderNo', key: 'workOrderNo' },
  { title: '产品编号', dataIndex: 'productNo', key: 'productNo' },
  { title: '产品名称', dataIndex: 'productName', key: 'productName' },
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '紧急程度', dataIndex: 'urgency', key: 'urgency' },
  { title: '负责人', dataIndex: 'assigneeName', key: 'assigneeName' },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
  { title: '操作', key: 'action', width: 200 },
]

// 模拟数据
const mockData = ref([
  {
    key: '1',
    workOrderNo: 'WO-2024-001',
    productNo: 'P-001',
    productName: '铝合金压铸件A',
    status: 'processing',
    urgency: 'high',
    assigneeName: '张三',
    createTime: '2024-01-15 09:30:00',
  },
  {
    key: '2',
    workOrderNo: 'WO-2024-002',
    productNo: 'P-002',
    productName: '铝合金压铸件B',
    status: 'pending',
    urgency: 'medium',
    assigneeName: '李四',
    createTime: '2024-01-15 10:15:00',
  },
])

// 事件处理函数
const handleSearch = () => {
  console.log('搜索参数:', searchForm.value)
  message.success('搜索完成')
}

const handleToggle = (expanded: boolean) => {
  message.info(`搜索条件已${expanded ? '展开' : '收起'}`)
}

const handleAdd = () => {
  message.info('新建工单')
}

const handleExport = () => {
  message.info('导出数据')
}

const handleReset = () => {
  searchForm.value = {
    workOrderNo: '',
    productNo: '',
    productName: '',
    status: '',
    urgency: '',
    createTimeRange: null,
    assignee: '',
    machine: '',
  }
  message.success('搜索条件已重置')
}

const handleRefresh = () => {
  message.success('数据已刷新')
}

const filterOption = (input: string, option: any) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 状态相关函数
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    draft: 'default',
    pending: 'orange',
    processing: 'blue',
    completed: 'green',
    cancelled: 'red',
  }
  return colors[status] || 'default'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    draft: '草稿',
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    cancelled: '已取消',
  }
  return texts[status] || status
}

const getUrgencyColor = (urgency: string) => {
  const colors: Record<string, string> = {
    low: 'green',
    medium: 'orange',
    high: 'red',
    urgent: 'magenta',
  }
  return colors[urgency] || 'default'
}

const getUrgencyText = (urgency: string) => {
  const texts: Record<string, string> = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急',
  }
  return texts[urgency] || urgency
}
</script>

<style lang="scss" scoped>
.search-layout-example {
  padding: $spacing-lg;

  .page-header {
    margin-bottom: $spacing-xl;

    h1 {
      color: $gray-800;
      font-size: $font-size-2xl;
      margin-bottom: $spacing-sm;
    }

    p {
      color: $gray-600;
      font-size: $font-size-base;
    }
  }

  .search-section {
    background: $white;
    padding: $spacing-lg;
    border-radius: $border-radius-lg;
    box-shadow: $shadow-sm;
    border: 1px solid $gray-200;
    margin-bottom: $spacing-lg;
  }

  .action-section {
    margin-bottom: $spacing-lg;
  }

  .result-section {
    :deep(.ant-card-head) {
      border-bottom: 1px solid $gray-200;
    }
  }

  // 搜索表单项的统一样式已经在组件内部处理
}
</style>
