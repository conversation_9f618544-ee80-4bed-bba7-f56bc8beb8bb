<template>
  <div class="search-layout-demo">
    <div class="demo-section">
      <h2>可折叠搜索条件布局组件演示</h2>

      <!-- 基础用法 -->
      <div class="demo-block">
        <h3>基础用法（3个条件）</h3>
        <CollapsibleSearchLayout
          :conditions="basicConditions"
          @search="handleBasicSearch"
          @toggle="handleToggle"
        >
          <template #condition-0>
            <a-form-item label="工单编号" class="search-form-item">
              <a-input v-model:value="basicForm.workOrderNo" placeholder="请输入工单编号" />
            </a-form-item>
          </template>
          <template #condition-1>
            <a-form-item label="产品名称" class="search-form-item">
              <a-input v-model:value="basicForm.productName" placeholder="请输入产品名称" />
            </a-form-item>
          </template>
          <template #condition-2>
            <a-form-item label="状态" class="search-form-item">
              <a-select v-model:value="basicForm.status" placeholder="请选择状态">
                <a-select-option value="pending">待处理</a-select-option>
                <a-select-option value="processing">处理中</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
              </a-select>
            </a-form-item>
          </template>
        </CollapsibleSearchLayout>
      </div>

      <!-- 多条件用法 -->
      <div class="demo-block">
        <h3>多条件用法（6个条件，支持展开/收起）</h3>
        <CollapsibleSearchLayout
          :conditions="advancedConditions"
          :default-expanded="false"
          @search="handleAdvancedSearch"
          @toggle="handleToggle"
        >
          <template #condition-0>
            <a-form-item label="工单编号" class="search-form-item">
              <a-input v-model:value="advancedForm.workOrderNo" placeholder="请输入工单编号" />
            </a-form-item>
          </template>
          <template #condition-1>
            <a-form-item label="产品编号" class="search-form-item">
              <a-input v-model:value="advancedForm.productNo" placeholder="请输入产品编号" />
            </a-form-item>
          </template>
          <template #condition-2>
            <a-form-item label="产品名称" class="search-form-item">
              <a-input v-model:value="advancedForm.productName" placeholder="请输入产品名称" />
            </a-form-item>
          </template>
          <template #condition-3>
            <a-form-item label="工单状态" class="search-form-item">
              <a-select v-model:value="advancedForm.status" placeholder="请选择状态">
                <a-select-option value="">全部</a-select-option>
                <a-select-option value="pending">待处理</a-select-option>
                <a-select-option value="processing">处理中</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
              </a-select>
            </a-form-item>
          </template>
          <template #condition-4>
            <a-form-item label="创建时间" class="search-form-item">
              <a-date-picker
                v-model:value="advancedForm.createTime"
                placeholder="请选择创建时间"
                style="width: 100%"
              />
            </a-form-item>
          </template>
          <template #condition-5>
            <a-form-item label="负责人" class="search-form-item">
              <a-input v-model:value="advancedForm.assignee" placeholder="请输入负责人" />
            </a-form-item>
          </template>
        </CollapsibleSearchLayout>
      </div>

      <!-- 自定义每行显示数量 -->
      <div class="demo-block">
        <h3>自定义布局（每行4个条件）</h3>
        <CollapsibleSearchLayout
          :conditions="customConditions"
          :items-per-row="4"
          :default-expanded="true"
          @search="handleCustomSearch"
        >
          <template #condition-0>
            <a-form-item label="字段1" class="search-form-item">
              <a-input placeholder="字段1" />
            </a-form-item>
          </template>
          <template #condition-1>
            <a-form-item label="字段2" class="search-form-item">
              <a-input placeholder="字段2" />
            </a-form-item>
          </template>
          <template #condition-2>
            <a-form-item label="字段3" class="search-form-item">
              <a-input placeholder="字段3" />
            </a-form-item>
          </template>
          <template #condition-3>
            <a-form-item label="字段4" class="search-form-item">
              <a-input placeholder="字段4" />
            </a-form-item>
          </template>
          <template #condition-4>
            <a-form-item label="字段5" class="search-form-item">
              <a-input placeholder="字段5" />
            </a-form-item>
          </template>
        </CollapsibleSearchLayout>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import CollapsibleSearchLayout from '@/components/CollapsibleSearchLayout/index.vue'

// 基础表单数据
const basicForm = ref({
  workOrderNo: '',
  productName: '',
  status: '',
})

// 高级表单数据
const advancedForm = ref({
  workOrderNo: '',
  productNo: '',
  productName: '',
  status: '',
  createTime: null,
  assignee: '',
})

// 搜索条件配置
const basicConditions = ref([{ label: '工单编号' }, { label: '产品名称' }, { label: '状态' }])

const advancedConditions = ref([
  { label: '工单编号' },
  { label: '产品编号' },
  { label: '产品名称' },
  { label: '工单状态' },
  { label: '创建时间' },
  { label: '负责人' },
])

const customConditions = ref([
  { label: '字段1' },
  { label: '字段2' },
  { label: '字段3' },
  { label: '字段4' },
  { label: '字段5' },
])

// 事件处理函数
const handleBasicSearch = () => {
  console.log('基础搜索:', basicForm.value)
  message.success('执行基础搜索')
}

const handleAdvancedSearch = () => {
  console.log('高级搜索:', advancedForm.value)
  message.success('执行高级搜索')
}

const handleCustomSearch = () => {
  console.log('自定义搜索')
  message.success('执行自定义搜索')
}

const handleToggle = (expanded: boolean) => {
  console.log('展开状态:', expanded)
  message.info(`搜索条件已${expanded ? '展开' : '收起'}`)
}
</script>

<style lang="scss" scoped>
.search-layout-demo {
  padding: $spacing-lg;

  .demo-section {
    h2 {
      color: $gray-800;
      margin-bottom: $spacing-xl;
      font-size: $font-size-2xl;
    }
  }

  .demo-block {
    margin-bottom: $spacing-2xl;
    padding: $spacing-lg;
    background: $white;
    border-radius: $border-radius-lg;
    box-shadow: $shadow-sm;
    border: 1px solid $gray-200;

    h3 {
      color: $gray-700;
      margin-bottom: $spacing-lg;
      font-size: $font-size-lg;
      font-weight: 600;
    }
  }

  // 搜索表单项的统一样式已经在组件内部处理
}
</style>
