<template>
  <div class="tabs-row">
    <div class="tabs">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        class="tabs-item"
        :class="{ selected: tab.active }"
        @click="handleTabClick(tab, index)"
      >
        <div class="tabs-item-title-wrapper">
          <component :is="tab.icon" />
          <span class="title">{{ tab.name }}</span>
        </div>
        <a-button
          class="more-btn"
          type="text"
          size="small"
          :icon="h(MoreOutlined)"
          @click.stop="handleMoreClick(tab, index)"
        />
      </div>
    </div>
    <div class="tabs-add-btn">
      <a-button
        class="add-btn"
        type="text"
        size="small"
        :icon="h(PlusOutlined)"
        @click="handleAddClick"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { h } from 'vue'
import { MoreOutlined, PlusOutlined } from '@ant-design/icons-vue'

// 定义 Tab 接口
interface Tab {
  name: string
  icon: any
  active: boolean
  [key: string]: any // 允许额外的属性
}

// 定义 Props
interface Props {
  tabs: Tab[]
}

// 定义 Emits
interface Emits {
  (e: 'tab-click', tab: Tab, index: number): void
  (e: 'more-click', tab: Tab, index: number): void
  (e: 'add-click'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

// 事件处理函数
const handleTabClick = (tab: Tab, index: number) => {
  emit('tab-click', tab, index)
}

// 菜单
const handleMoreClick = (tab: Tab, index: number) => {
  emit('more-click', tab, index)
}

// 添加
const handleAddClick = () => {
  emit('add-click')
}
</script>

<style lang="scss" scoped>
.tabs-row {
  display: flex;
  align-items: center;
  padding: 0 $spacing-lg;
  border-bottom: 1px solid $border-color;

  .tabs {
    display: flex;

    .tabs-item {
      position: relative;
      display: inline-flex;
      padding: $spacing-base $spacing-md;
      padding-right: $spacing-xs;
      cursor: pointer;

      &:first-child {
        padding-left: 0;
      }
      &.selected {
        .title {
          color: $primary-color;
        }

        .tabs-item-title-wrapper::after {
          background-color: $primary-color;
        }
      }

      .tabs-item-title-wrapper {
        cursor: pointer;
        align-items: center;
        display: flex;
        position: relative;

        &::after {
          content: '';
          background-color: transparent;
          height: 2px;
          position: absolute;
          bottom: -11px;
          left: 0;
          right: 0;
        }

        span.title {
          margin-left: $spacing-sm;
        }
      }

      .more-btn {
        margin-left: $spacing-xs;

        &::after {
          content: '';
          background-color: #e5e5e5;
          width: 1px;
          height: 24px;
          position: absolute;
          top: -2px;
          right: -5px;
        }
      }
    }
  }

  .tabs-add-btn {
    padding-left: $spacing-sm;
  }
}
</style>
