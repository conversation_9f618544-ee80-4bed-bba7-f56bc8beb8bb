<template>
  <div class="collapsible-search-layout">
    <a-row :gutter="16" class="search-form-row">
      <!-- 搜索条件区域 -->
      <template v-for="(condition, index) in visibleConditions" :key="index">
        <a-col :span="colSpan" class="search-condition-col">
          <slot :name="`condition-${index}`" :condition="condition" :index="index">
            <!-- 默认插槽内容，如果没有提供具体的搜索条件 -->
            <div class="default-condition">
              {{ condition.label || `搜索条件 ${index + 1}` }}
            </div>
          </slot>
        </a-col>
      </template>

      <!-- 查询按钮区域 -->
      <a-col :span="buttonColSpan" class="search-button-col">
        <div class="button-group">
          <a-button type="primary" :icon="h(SearchOutlined)" @click="handleSearch"> 查询 </a-button>

          <!-- 展开/收起按钮 -->
          <a-button v-if="showToggleButton" type="text" class="toggle-button" @click="handleToggle">
            <template #icon>
              <DownOutlined :class="{ 'toggle-icon-expanded': expanded }" class="toggle-icon" />
            </template>
            {{ expanded ? '收起' : '展开' }}
          </a-button>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import { SearchOutlined, DownOutlined } from '@ant-design/icons-vue'

// 定义搜索条件接口
interface SearchCondition {
  label?: string
  value?: any
  [key: string]: any
}

// 定义组件属性
interface Props {
  conditions?: SearchCondition[]
  defaultExpanded?: boolean
  itemsPerRow?: number
  minItemsToShowToggle?: number
}

// 定义事件
interface Emits {
  (e: 'search'): void
  (e: 'toggle', expanded: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  conditions: () => [],
  defaultExpanded: false,
  itemsPerRow: 3,
  minItemsToShowToggle: 3,
})

const emit = defineEmits<Emits>()

// 响应式数据
const expanded = ref(props.defaultExpanded)

// 计算属性
const showToggleButton = computed(() => {
  return props.conditions.length > props.minItemsToShowToggle
})

const visibleConditions = computed(() => {
  if (!showToggleButton.value || expanded.value) {
    return props.conditions
  }
  return props.conditions.slice(0, props.minItemsToShowToggle)
})

const colSpan = computed(() => {
  // 24 / itemsPerRow，确保每行显示指定数量的条件
  return Math.floor(24 / props.itemsPerRow)
})

const buttonColSpan = computed(() => {
  const conditionsInCurrentRow = visibleConditions.value.length % props.itemsPerRow
  const remainingCols =
    conditionsInCurrentRow === 0 ? 0 : props.itemsPerRow - conditionsInCurrentRow

  if (remainingCols === 0) {
    // 如果当前行已满，按钮占用新的一行
    return 24
  } else {
    // 按钮占用当前行剩余空间
    return remainingCols * colSpan.value
  }
})

// 事件处理函数
const handleSearch = () => {
  emit('search')
}

const handleToggle = () => {
  expanded.value = !expanded.value
  emit('toggle', expanded.value)
}
</script>

<style lang="scss" scoped>
.collapsible-search-layout {
  .search-form-row {
    align-items: flex-end;
  }

  .search-condition-col {
    margin-bottom: $spacing-md;

    // 确保所有搜索条件列有统一的高度和对齐
    :deep(.ant-form-item) {
      margin-bottom: 0;
      width: 100%;

      .ant-form-item-label {
        width: 100%;
        text-align: left;
        padding-bottom: 4px;

        > label {
          font-size: $font-size-sm;
          color: $gray-700;
          font-weight: 500;
          // 确保标签有固定的最小宽度，避免因文字长度不同导致的对齐问题
          min-width: 60px;
          display: inline-block;
        }
      }

      .ant-form-item-control {
        width: 100%;

        .ant-form-item-control-input {
          width: 100%;

          .ant-form-item-control-input-content {
            width: 100%;

            // 确保所有输入控件都占满容器宽度
            .ant-input,
            .ant-select,
            .ant-picker,
            .ant-cascader-picker {
              width: 100% !important;
            }
          }
        }
      }
    }
  }

  .search-button-col {
    margin-bottom: $spacing-md;

    .button-group {
      display: flex;
      align-items: center;
      gap: $spacing-sm;
      justify-content: flex-end;
      height: 32px; // 与输入框高度保持一致
    }
  }

  .toggle-button {
    color: $primary-color;
    padding: 4px 8px;

    .toggle-icon {
      transition: transform 0.3s ease;

      &.toggle-icon-expanded {
        transform: rotate(180deg);
      }
    }

    &:hover {
      color: $primary-dark;
      background-color: rgba($primary-color, 0.1);
    }
  }

  .default-condition {
    padding: 8px 12px;
    background-color: $gray-100;
    border-radius: $border-radius-sm;
    color: $gray-600;
    font-size: $font-size-sm;
    text-align: center;
    width: 100%;
    box-sizing: border-box;
  }
}
</style>
