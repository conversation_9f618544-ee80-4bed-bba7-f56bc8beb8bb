# CollapsibleSearchLayout 可折叠搜索条件布局组件

一个基于 Ant Design Vue 的可折叠搜索条件布局组件，支持响应式布局和灵活的搜索条件配置。

## 功能特性

- ✅ 可折叠的搜索条件布局
- ✅ 响应式Grid布局（基于Ant Design Vue的Row/Col）
- ✅ 支持自定义每行显示的条件数量
- ✅ 灵活的插槽系统，支持任意搜索表单控件
- ✅ 内置展开/收起动画效果
- ✅ 查询按钮自动定位到合适位置
- ✅ TypeScript支持

## 基础用法

```vue
<template>
  <CollapsibleSearchLayout
    :conditions="searchConditions"
    @search="handleSearch"
    @toggle="handleToggle"
  >
    <template #condition-0>
      <a-form-item label="工单编号">
        <a-input v-model:value="form.workOrderNo" placeholder="请输入工单编号" />
      </a-form-item>
    </template>

    <template #condition-1>
      <a-form-item label="产品名称">
        <a-input v-model:value="form.productName" placeholder="请输入产品名称" />
      </a-form-item>
    </template>

    <template #condition-2>
      <a-form-item label="状态">
        <a-select v-model:value="form.status" placeholder="请选择状态">
          <a-select-option value="pending">待处理</a-select-option>
          <a-select-option value="completed">已完成</a-select-option>
        </a-select>
      </a-form-item>
    </template>
  </CollapsibleSearchLayout>
</template>

<script setup>
import CollapsibleSearchLayout from '@/components/CollapsibleSearchLayout'

const searchConditions = ref([{ label: '工单编号' }, { label: '产品名称' }, { label: '状态' }])

const form = ref({
  workOrderNo: '',
  productName: '',
  status: '',
})

const handleSearch = () => {
  console.log('搜索参数:', form.value)
}

const handleToggle = (expanded) => {
  console.log('展开状态:', expanded)
}
</script>
```

## API

### Props

| 参数                 | 说明                          | 类型                | 默认值  |
| -------------------- | ----------------------------- | ------------------- | ------- |
| conditions           | 搜索条件配置数组              | `SearchCondition[]` | `[]`    |
| defaultExpanded      | 默认是否展开                  | `boolean`           | `false` |
| itemsPerRow          | 每行显示的条件数量            | `number`            | `3`     |
| minItemsToShowToggle | 显示展开/收起按钮的最小条件数 | `number`            | `3`     |

### SearchCondition 接口

```typescript
interface SearchCondition {
  label?: string
  value?: any
  [key: string]: any
}
```

### Events

| 事件名 | 说明                    | 回调参数              |
| ------ | ----------------------- | --------------------- |
| search | 点击查询按钮时触发      | -                     |
| toggle | 展开/收起状态改变时触发 | `(expanded: boolean)` |

### Slots

| 插槽名            | 说明                      | 参数                   |
| ----------------- | ------------------------- | ---------------------- |
| condition-{index} | 第{index}个搜索条件的内容 | `{ condition, index }` |

## 高级用法

### 自定义每行显示数量

```vue
<CollapsibleSearchLayout :conditions="conditions" :items-per-row="4" @search="handleSearch">
  <!-- 搜索条件插槽 -->
</CollapsibleSearchLayout>
```

### 默认展开状态

```vue
<CollapsibleSearchLayout :conditions="conditions" :default-expanded="true" @search="handleSearch">
  <!-- 搜索条件插槽 -->
</CollapsibleSearchLayout>
```

### 自定义展开阈值

```vue
<CollapsibleSearchLayout
  :conditions="conditions"
  :min-items-to-show-toggle="5"
  @search="handleSearch"
>
  <!-- 搜索条件插槽 -->
</CollapsibleSearchLayout>
```

## 布局规则

1. **默认布局**: 每行显示3个搜索条件
2. **查询按钮位置**:
   - 如果当前行未满，查询按钮显示在当前行的剩余空间
   - 如果当前行已满，查询按钮显示在新的一行
3. **展开/收起按钮**: 只有当搜索条件数量超过 `minItemsToShowToggle` 时才显示
4. **响应式**: 基于Ant Design Vue的Grid系统，自动适配不同屏幕尺寸

## 样式定制

组件使用项目的SCSS变量系统，可以通过修改以下变量来定制样式：

```scss
// 主要颜色
$primary-color: #02b980;
$primary-dark: #01a16f;

// 间距
$spacing-sm: 8px;
$spacing-md: 16px;

// 边框圆角
$border-radius-sm: 4px;

// 字体大小
$font-size-sm: 14px;
```

## 注意事项

1. 确保项目已安装并配置 Ant Design Vue
2. 搜索条件的插槽名必须按照 `condition-{index}` 的格式命名
3. 组件内部不处理具体的表单验证和数据提交，需要在父组件中实现
4. 建议在搜索条件较多（>3个）的场景下使用此组件

## 完整示例

查看 `src/views/demo/SearchLayoutExample.vue` 文件获取完整的使用示例。
